
'use client';

import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Coins, Info, Loader2, Globe, Gem, Sparkles } from 'lucide-react';
import { getGoldAndSilverPrices } from '@/lib/actions/gold';
import { COUNTRIES_CURRENCIES } from '@/lib/constants/currencies';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

type PriceData = Awaited<ReturnType<typeof getGoldAndSilverPrices>>;

interface ZakatCalculatorToolProps {
  initialData: PriceData;
}

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." }).nonnegative('لا يمكن إدخال قيمة سالبة.').default(0);

const FormSchema = z.object({
  cash: requiredNumber(),
  goldGrams: requiredNumber(),
  silverGrams: requiredNumber(),
  debts: requiredNumber(),
});

interface Result {
  totalWealth: number;
  zakatDue: number;
  nisabGoldValue: number;
  nisabSilverValue: number;
  isGoldNisabMet: boolean;
  isSilverNisabMet: boolean;
  isCashNisabMet: boolean;
}

const NISAB_GOLD_GRAMS = 85;
const NISAB_SILVER_GRAMS = 595;

export function ZakatCalculatorTool({ initialData }: ZakatCalculatorToolProps) {
  const [result, setResult] = useState<Result | null>(null);
  const [currentData, setCurrentData] = useState<PriceData>(initialData);
  const [isPending, startTransition] = useTransition();

  const { success, prices, error, selectedCurrency } = currentData;
  const goldPricePerGram = success ? prices.gold.local.perGram : 0;
  const silverPricePerGram = success ? prices.silver.local.perGram : 0;
  
  const nisabGoldValue = goldPricePerGram * NISAB_GOLD_GRAMS;
  const nisabSilverValue = silverPricePerGram * NISAB_SILVER_GRAMS;
  const nisabForCash = Math.min(nisabGoldValue, nisabSilverValue); // الأحوط استخدام الأقل للفقير

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { cash: 0, goldGrams: 0, silverGrams: 0, debts: 0 },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    // حساب إجمالي الثروة الزكوية
    let zakatableWealth = 0;

    // إضافة قيمة الذهب (بغض النظر عن النصاب المنفرد)
    const goldValue = data.goldGrams * goldPricePerGram;
    zakatableWealth += goldValue;

    // إضافة قيمة الفضة (بغض النظر عن النصاب المنفرد)
    const silverValue = data.silverGrams * silverPricePerGram;
    zakatableWealth += silverValue;

    // إضافة النقود
    zakatableWealth += data.cash;

    // خصم الديون من إجمالي الثروة
    zakatableWealth -= data.debts;

    // التحقق من بلوغ النصاب لكل نوع منفرداً (للعرض فقط)
    const isGoldNisabMet = data.goldGrams >= NISAB_GOLD_GRAMS;
    const isSilverNisabMet = data.silverGrams >= NISAB_SILVER_GRAMS;
    const isCashNisabMet = data.cash >= nisabForCash;

    // التحقق من بلوغ النصاب الإجمالي (هذا هو المهم للحساب)
    const totalWealthBeforeDebts = goldValue + silverValue + data.cash;
    const isNisabMet = totalWealthBeforeDebts >= nisabForCash;

    // إذا لم يبلغ النصاب أو كانت الثروة الصافية سالبة
    if (!isNisabMet || zakatableWealth <= 0) {
       setResult({
        totalWealth: Math.max(0, zakatableWealth),
        zakatDue: 0,
        nisabGoldValue,
        nisabSilverValue,
        isGoldNisabMet,
        isSilverNisabMet,
        isCashNisabMet,
      });
      return;
    }

    // حساب الزكاة: 2.5% من الثروة الصافية
    const zakatDue = zakatableWealth * 0.025;

    setResult({
      totalWealth: zakatableWealth,
      zakatDue,
      nisabGoldValue,
      nisabSilverValue,
      isGoldNisabMet,
      isSilverNisabMet,
      isCashNisabMet,
    });
  }
  
  const handleCurrencyChange = (newCurrency: string) => {
    startTransition(async () => {
      const newData = await getGoldAndSilverPrices(newCurrency);
      setCurrentData(newData);
      setResult(null);
    });
  };

  if (!success && !currentData.prices) {
    return (
      <Alert variant="destructive">
        <Info className="h-4 w-4" />
        <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
        <AlertDescription>
          لم نتمكن من تحميل أسعار الذهب والفضة لتحديد النصاب. الرجاء المحاولة مرة أخرى لاحقًا.
          <p className="mt-2 text-xs">{error}</p>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حساب زكاة المال</CardTitle>
        <CardDescription>أدخل أصولك وديونك لحساب الزكاة الواجبة بدقة.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 space-y-4">
            <div className="flex items-center gap-4">
                <Globe className="h-5 w-5 text-muted-foreground" />
                <div className="flex-1">
                  <label className="text-sm font-medium mb-2 block">اختر عملة بلدك:</label>
                  <Select 
                    value={selectedCurrency} 
                    onValueChange={handleCurrencyChange}
                    disabled={isPending}
                  >
                    <SelectTrigger className="w-full md:w-80">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(COUNTRIES_CURRENCIES).map(([code, info]) => (
                        <SelectItem key={code} value={code}>
                           {info.name} ({info.currency})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
            </div>
            
            <Alert className="relative grid grid-cols-1 md:grid-cols-2 gap-4">
              {isPending && (
                <div className="absolute inset-0 bg-background/80 flex items-center justify-center rounded-lg">
                  <Loader2 className="h-5 w-5 animate-spin" />
                </div>
              )}
              <div>
                <AlertTitle className="flex items-center gap-2"><Gem className="h-4 w-4"/>نصاب الذهب</AlertTitle>
                <AlertDescription>
                    {NISAB_GOLD_GRAMS} جرامًا. قيمته الحالية:{' '}
                    <span className="font-bold">
                    {nisabGoldValue.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}
                    </span>.
                </AlertDescription>
              </div>
              <div>
                <AlertTitle className="flex items-center gap-2"><Sparkles className="h-4 w-4" />نصاب الفضة</AlertTitle>
                 <AlertDescription>
                    {NISAB_SILVER_GRAMS} جرامًا. قيمته الحالية:{' '}
                    <span className="font-bold">
                    {nisabSilverValue.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}
                    </span>.
                </AlertDescription>
              </div>
               {error && <span className="block text-amber-600 text-xs mt-1 md:col-span-2">({error})</span>}
            </Alert>
        </div>


        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="goldGrams"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>وزن الذهب (بالجرام)</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="silverGrams"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>وزن الفضة (بالجرام)</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="cash"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>المال او النقود الذي تملك</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="debts"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الديون المستحقة عليك</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isPending}>
              <Coins className="ml-2 h-4 w-4" />
              احسب الزكاة
            </Button>
          </form>
        </Form>

        {result && (
          <div className="mt-8 text-center space-y-4">
             <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-sm text-muted-foreground">مقدار الزكاة الواجبة</p>
                <p className="text-3xl font-bold font-mono text-primary">
                  {result.zakatDue.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}
                </p>
              </div>
            {result.zakatDue === 0 && (
              <Alert variant="default" className="mt-4">
                <Info className="h-4 w-4" />
                <AlertTitle>لم تجب عليك الزكاة بعد</AlertTitle>
                <AlertDescription>
                  إجمالي أموالك (قبل خصم الديون) لم يبلغ النصاب المطلوب، أو أن صافي ثروتك (بعد خصم الديون) أصبح صفراً أو أقل.
                  النصاب المطلوب هو الأقل بين نصاب الذهب والفضة، وقيمته الآن {' '}
                  {nisabForCash.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}.
                  <br />
                  <span className="text-xs mt-2 block">
                    صافي ثروتك الحالية: {result.totalWealth.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}
                  </span>
                </AlertDescription>
              </Alert>
            )}

            {result.zakatDue > 0 && (
              <Alert variant="default" className="mt-4 bg-green-50 border-green-200">
                <Info className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">تفاصيل الحساب</AlertTitle>
                <AlertDescription className="text-green-700">
                  <div className="space-y-1 text-sm">
                    <div>صافي الثروة الخاضعة للزكاة: {result.totalWealth.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}</div>
                    <div>نسبة الزكاة: 2.5%</div>
                    <div className="font-bold">مقدار الزكاة الواجبة: {result.zakatDue.toLocaleString('ar-SA-u-nu-latn', { style: 'currency', currency: selectedCurrency })}</div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
